using Cysharp.Threading.Tasks;
using DG.Tweening;
using UnityEngine;
public class NinMegaAnimator : MonoBehaviour
{
    public Transform circleTransform;
    public GameObject effectPrefab;
    public Transform imagePrefab; //mega的logo
    public async UniTask PlayEffect() {
        circleTransform.localScale = Vector3.one * 0f;
        circleTransform.gameObject.SetActive(true);
        effectPrefab.SetActive(true);
        RestartParticles(effectPrefab);
        StopFading(circleTransform);
        await UniTask.Delay(1000);
        circleTransform.DOScale(Vector3.one * 6, 0.8f); //.SetEase(Ease.OutBounce)
        await UniTask.Delay(600);
        imagePrefab.gameObject.SetActive(true);
        //让imagePrefab 对着镜头
        imagePrefab.LookAt(Camera.main.transform);
        imagePrefab.localPosition = new Vector3(0, 2.5f, 0);
        imagePrefab.localScale = Vector3.one * 0.0f;
        imagePrefab.DOLocalMoveY(4.5f, 1.2f);
        imagePrefab.DOScale(Vector3.one, 1.2f).onComplete = () => {
            imagePrefab.gameObject.SetActive(false);
        };
        // circleTransform.DOScale(Vector3.one * 6, 0.2f).SetEase(Ease.OutBounce);
        await UniTask.Delay(900);
        circleTransform.gameObject.SetActive(false);
        // await UniTask.Delay(800);
        // effectPrefab.gameObject.SetActive(false);
        await UniTask.Delay(400);
        effectPrefab.gameObject.SetActive(false);
    }
    private void RestartParticles(GameObject prefabRoot)
    {
        ParticleSystem[] particles = prefabRoot.GetComponentsInChildren<ParticleSystem>();
        foreach (var ps in particles)
        {
            ps.Clear();
            ps.Play();
        }
    }
    public void StopFading(Transform transform)
    {
        transform.DOComplete(); // 立即完成当前动画，设置到最终状态
        transform.DOKill(); // 停止所有相关动画
        // spriteRenderer.DOComplete(); // 立即完成当前动画，设置到最终状态
        // spriteRenderer.DOKill(); // 停止所有相关动画
    }
}