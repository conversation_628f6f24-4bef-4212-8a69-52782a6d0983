using Cysharp.Threading.Tasks;
using DG.Tweening;
using UnityEngine;
public class NinMaxAnimator : MonoBehaviour
{
    public Transform circleTransform;
    public GameObject effectPrefab;
    public Transform imagePrefab; //mega的logo
    public async UniTask PlayEffect() {
        effectPrefab.SetActive(true);
        // RestartParticles(effectPrefab);
        // StopFading(circleTransform);
        // await UniTask.Delay(1000);
        // circleTransform.localScale = Vector3.one * 0.1f;
        // circleTransform.gameObject.SetActive(true);
        // circleTransform.DOScale(Vector3.one * 6, 0.8f); //.SetEase(Ease.OutBounce)
        // await UniTask.Delay(600);
        // imagePrefab.gameObject.SetActive(true);
        // // circleTransform.DOScale(Vector3.one * 6, 0.2f).SetEase(Ease.OutBounce);
        // await UniTask.Delay(800);
        // circleTransform.gameObject.SetActive(false);
        // effectPrefab.gameObject.SetActive(false);
        // //让imagePrefab 对着镜头
        // imagePrefab.LookAt(Camera.main.transform);
        // imagePrefab.localPosition = new Vector3(0, 2.5f, 0);
        // imagePrefab.localScale = Vector3.one * 0.0f;
        // imagePrefab.DOLocalMoveY(3.5f, 0.5f);
        // imagePrefab.DOScale(Vector3.one, 0.5f).onComplete = () => {
        //     imagePrefab.gameObject.SetActive(false);
        // };
        // await UniTask.Delay(400);
    }
    private void RestartParticles(GameObject prefabRoot)
    {
        ParticleSystem[] particles = prefabRoot.GetComponentsInChildren<ParticleSystem>();
        foreach (var ps in particles)
        {
            ps.Clear();
            ps.Play();
        }
    }
    public void StopFading(Transform transform)
    {
        transform.DOComplete(); // 立即完成当前动画，设置到最终状态
        transform.DOKill(); // 停止所有相关动画
        // spriteRenderer.DOComplete(); // 立即完成当前动画，设置到最终状态
        // spriteRenderer.DOKill(); // 停止所有相关动画
    }
}